<?php
require_once '../config/config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$productId = intval($_POST['product_id'] ?? 0);

if ($productId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid product']);
    exit;
}

try {
    if (isLoggedIn()) {
        // User is logged in, remove from database
        $userId = $_SESSION['user_id'];
        
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$userId, $productId]);
        
    } else {
        // User is not logged in, remove from session
        if (isset($_SESSION['cart'][$productId])) {
            unset($_SESSION['cart'][$productId]);
        }
    }
    
    echo json_encode(['success' => true, 'message' => 'Item removed from cart']);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error removing item from cart']);
}
?>
