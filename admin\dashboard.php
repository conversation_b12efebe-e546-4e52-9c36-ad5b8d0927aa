<?php
$pageTitle = 'Admin Dashboard';
include '../includes/header.php';

// Require admin access
requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Get dashboard statistics
$stats = [];

// Total products
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM products");
$stmt->execute();
$stats['products'] = $stmt->fetchColumn();

// Total orders
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders");
$stmt->execute();
$stats['orders'] = $stmt->fetchColumn();

// Total users
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'customer'");
$stmt->execute();
$stats['users'] = $stmt->fetchColumn();

// Total revenue
$stmt = $conn->prepare("SELECT SUM(total_amount) as total FROM orders WHERE payment_status = 'completed'");
$stmt->execute();
$stats['revenue'] = $stmt->fetchColumn() ?? 0;

// Recent orders
$stmt = $conn->prepare("SELECT o.*, u.name as customer_name 
                       FROM orders o 
                       JOIN users u ON o.user_id = u.id 
                       ORDER BY o.created_at DESC LIMIT 5");
$stmt->execute();
$recentOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Low stock products
$stmt = $conn->prepare("SELECT * FROM products WHERE stock_quantity <= 5 ORDER BY stock_quantity ASC LIMIT 5");
$stmt->execute();
$lowStockProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Order status distribution
$stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM orders GROUP BY status");
$stmt->execute();
$orderStatusData = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container" style="margin-top: 2rem;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
        <div>
            <a href="../index.php" class="btn btn-secondary">
                <i class="fas fa-home"></i> Back to Store
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
        <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 12px; text-align: center;">
            <i class="fas fa-box" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.8;"></i>
            <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem;"><?php echo number_format($stats['products']); ?></h3>
            <p style="margin: 0; opacity: 0.9;">Total Products</p>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 2rem; border-radius: 12px; text-align: center;">
            <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.8;"></i>
            <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem;"><?php echo number_format($stats['orders']); ?></h3>
            <p style="margin: 0; opacity: 0.9;">Total Orders</p>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 2rem; border-radius: 12px; text-align: center;">
            <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.8;"></i>
            <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem;"><?php echo number_format($stats['users']); ?></h3>
            <p style="margin: 0; opacity: 0.9;">Total Customers</p>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 2rem; border-radius: 12px; text-align: center;">
            <i class="fas fa-dollar-sign" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.8;"></i>
            <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem;"><?php echo formatPrice($stats['revenue']); ?></h3>
            <p style="margin: 0; opacity: 0.9;">Total Revenue</p>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mb-3">
        <div class="card-header">
            <h3>Quick Actions</h3>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <a href="products.php" class="btn btn-primary" style="padding: 1rem; text-align: center;">
                    <i class="fas fa-box" style="display: block; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                    Manage Products
                </a>
                <a href="orders.php" class="btn btn-success" style="padding: 1rem; text-align: center;">
                    <i class="fas fa-shopping-cart" style="display: block; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                    Manage Orders
                </a>
                <a href="users.php" class="btn btn-info" style="padding: 1rem; text-align: center;">
                    <i class="fas fa-users" style="display: block; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                    Manage Users
                </a>
                <a href="add-product.php" class="btn btn-warning" style="padding: 1rem; text-align: center;">
                    <i class="fas fa-plus" style="display: block; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                    Add New Product
                </a>
            </div>
        </div>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <!-- Recent Orders -->
        <div class="card">
            <div class="card-header">
                <h3>Recent Orders</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($recentOrders)): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td>#<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                        <td><?php echo formatPrice($order['total_amount']); ?></td>
                                        <td>
                                            <span class="badge btn-<?php echo $order['status'] === 'pending' ? 'warning' : ($order['status'] === 'delivered' ? 'success' : 'info'); ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="orders.php" class="btn btn-primary">View All Orders</a>
                    </div>
                <?php else: ?>
                    <p class="text-center">No orders yet.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Low Stock Alert -->
        <div class="card">
            <div class="card-header">
                <h3>Low Stock Alert</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($lowStockProducts)): ?>
                    <?php foreach ($lowStockProducts as $product): ?>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                            <div>
                                <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                <br>
                                <small style="color: #666;">Stock: <?php echo $product['stock_quantity']; ?> units</small>
                            </div>
                            <span class="badge btn-<?php echo $product['stock_quantity'] == 0 ? 'danger' : 'warning'; ?>">
                                <?php echo $product['stock_quantity'] == 0 ? 'Out of Stock' : 'Low Stock'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                    <div class="text-center mt-2">
                        <a href="products.php" class="btn btn-warning">Manage Stock</a>
                    </div>
                <?php else: ?>
                    <p class="text-center">All products are well stocked!</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Order Status Chart -->
    <div class="card mt-3">
        <div class="card-header">
            <h3>Order Status Distribution</h3>
        </div>
        <div class="card-body">
            <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
                <?php foreach ($orderStatusData as $statusData): ?>
                    <div class="text-center">
                        <div style="width: 100px; height: 100px; border-radius: 50%; background: <?php 
                            echo $statusData['status'] === 'pending' ? '#f39c12' : 
                                ($statusData['status'] === 'delivered' ? '#27ae60' : 
                                ($statusData['status'] === 'cancelled' ? '#e74c3c' : '#3498db')); 
                        ?>; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">
                            <?php echo $statusData['count']; ?>
                        </div>
                        <p style="margin: 0; text-transform: capitalize;"><?php echo $statusData['status']; ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
