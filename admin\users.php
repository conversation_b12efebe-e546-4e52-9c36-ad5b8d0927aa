<?php
$pageTitle = 'Manage Users';
include '../includes/header.php';

// Require admin access
requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Handle role update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    $userId = intval($_POST['user_id']);
    $newRole = sanitize($_POST['role']);
    
    // Prevent admin from changing their own role
    if ($userId == $_SESSION['user_id']) {
        setMessage('You cannot change your own role.', 'danger');
    } elseif (in_array($newRole, ['customer', 'admin'])) {
        try {
            $stmt = $conn->prepare("UPDATE users SET role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$newRole, $userId]);
            setMessage('User role updated successfully.', 'success');
        } catch (PDOException $e) {
            setMessage('Error updating user role.', 'danger');
        }
    }
    
    redirect('users.php');
}

// Get filter parameters
$role = $_GET['role'] ?? '';
$search = $_GET['search'] ?? '';
$sort = $_GET['sort'] ?? 'created_at';
$order = $_GET['order'] ?? 'DESC';

// Build query
$whereConditions = [];
$params = [];

if (!empty($role)) {
    $whereConditions[] = "role = ?";
    $params[] = $role;
}

if (!empty($search)) {
    $whereConditions[] = "(name LIKE ? OR email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Validate sort and order
$allowedSorts = ['name', 'email', 'role', 'created_at'];
$allowedOrders = ['ASC', 'DESC'];
$sort = in_array($sort, $allowedSorts) ? $sort : 'created_at';
$order = in_array($order, $allowedOrders) ? $order : 'DESC';

$query = "SELECT * FROM users $whereClause ORDER BY $sort $order";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user statistics
$stmt = $conn->prepare("SELECT role, COUNT(*) as count FROM users GROUP BY role");
$stmt->execute();
$roleCounts = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $roleCounts[$row['role']] = $row['count'];
}

// Get total orders per user
$userOrders = [];
$stmt = $conn->prepare("SELECT user_id, COUNT(*) as order_count, SUM(total_amount) as total_spent FROM orders GROUP BY user_id");
$stmt->execute();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $userOrders[$row['user_id']] = $row;
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-users"></i> Manage Users</h1>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <!-- User Statistics -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
        <div class="stat-card" style="background: #3498db; color: white; padding: 1.5rem; border-radius: 8px; text-align: center;">
            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
            <h3><?php echo array_sum($roleCounts); ?></h3>
            <p>Total Users</p>
        </div>
        <div class="stat-card" style="background: #27ae60; color: white; padding: 1.5rem; border-radius: 8px; text-align: center;">
            <i class="fas fa-user" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
            <h3><?php echo $roleCounts['customer'] ?? 0; ?></h3>
            <p>Customers</p>
        </div>
        <div class="stat-card" style="background: #e74c3c; color: white; padding: 1.5rem; border-radius: 8px; text-align: center;">
            <i class="fas fa-user-shield" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
            <h3><?php echo $roleCounts['admin'] ?? 0; ?></h3>
            <p>Administrators</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="d-flex gap-2" style="flex-wrap: wrap; align-items: end;">
                <div class="form-group" style="min-width: 250px;">
                    <label for="search">Search Users</label>
                    <input type="text" id="search" name="search" class="form-control" 
                           placeholder="Search by name or email..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="form-group" style="min-width: 120px;">
                    <label for="role">Role</label>
                    <select id="role" name="role" class="form-control">
                        <option value="">All Roles</option>
                        <option value="customer" <?php echo $role === 'customer' ? 'selected' : ''; ?>>Customer</option>
                        <option value="admin" <?php echo $role === 'admin' ? 'selected' : ''; ?>>Admin</option>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 120px;">
                    <label for="sort">Sort By</label>
                    <select id="sort" name="sort" class="form-control">
                        <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Join Date</option>
                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                        <option value="email" <?php echo $sort === 'email' ? 'selected' : ''; ?>>Email</option>
                        <option value="role" <?php echo $sort === 'role' ? 'selected' : ''; ?>>Role</option>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 100px;">
                    <label for="order">Order</label>
                    <select id="order" name="order" class="form-control">
                        <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>Descending</option>
                        <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <?php if (!empty($users)): ?>
        <div class="card">
            <div class="card-header">
                <h3>Users (<?php echo count($users); ?> found)</h3>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Contact</th>
                                <th>Role</th>
                                <th>Orders</th>
                                <th>Total Spent</th>
                                <th>Join Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 1rem;">
                                            <div class="avatar" style="width: 40px; height: 40px; background: #3498db; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                                                <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge btn-info" style="font-size: 10px; margin-left: 5px;">You</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['email']); ?></strong>
                                            <?php if (!empty($user['phone'])): ?>
                                                <br><small style="color: #666;"><?php echo htmlspecialchars($user['phone']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                            <span class="badge btn-<?php echo $user['role'] === 'admin' ? 'danger' : 'primary'; ?>">
                                                <?php echo ucfirst($user['role']); ?>
                                            </span>
                                        <?php else: ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <select name="role" class="form-control form-control-sm" style="width: auto; display: inline-block;" onchange="this.form.submit()">
                                                    <option value="customer" <?php echo $user['role'] === 'customer' ? 'selected' : ''; ?>>Customer</option>
                                                    <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                                </select>
                                                <input type="hidden" name="update_role" value="1">
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo $userOrders[$user['id']]['order_count'] ?? 0; ?></strong> orders
                                    </td>
                                    <td>
                                        <strong><?php echo formatPrice($userOrders[$user['id']]['total_spent'] ?? 0); ?></strong>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <button class="btn btn-info btn-sm" onclick="viewUserDetails(<?php echo $user['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-users" style="font-size: 4rem; color: #ccc; margin-bottom: 1rem;"></i>
            <h3>No Users Found</h3>
            <p>Try adjusting your search criteria.</p>
        </div>
    <?php endif; ?>
</div>

<script>
function viewUserDetails(userId) {
    // In a real application, this would show detailed user information
    alert('User details for User ID: ' + userId);
}
</script>

<?php include '../includes/footer.php'; ?>
