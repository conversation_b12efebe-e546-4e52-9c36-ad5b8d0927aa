<?php
$pageTitle = 'Add Product';
include '../includes/header.php';

// Require admin access
requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Get categories
$stmt = $conn->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $categoryId = intval($_POST['category_id'] ?? 0);
    $stockQuantity = intval($_POST['stock_quantity'] ?? 0);
    $size = sanitize($_POST['size'] ?? '');
    $color = sanitize($_POST['color'] ?? '');
    
    $errors = [];
    
    // Validation
    if (empty($name)) {
        $errors[] = 'Product name is required.';
    }
    
    if ($price <= 0) {
        $errors[] = 'Price must be greater than 0.';
    }
    
    if ($categoryId <= 0) {
        $errors[] = 'Please select a category.';
    }
    
    if ($stockQuantity < 0) {
        $errors[] = 'Stock quantity cannot be negative.';
    }
    
    // Handle image upload
    $imageName = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/products/';
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        $fileType = $_FILES['image']['type'];
        $fileSize = $_FILES['image']['size'];
        
        if (!in_array($fileType, $allowedTypes)) {
            $errors[] = 'Invalid image type. Please upload JPEG, PNG, GIF, or WebP images.';
        }
        
        if ($fileSize > $maxSize) {
            $errors[] = 'Image size must be less than 5MB.';
        }
        
        if (empty($errors)) {
            $extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $imageName = uniqid() . '.' . $extension;
            $uploadPath = $uploadDir . $imageName;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
                $errors[] = 'Failed to upload image.';
                $imageName = '';
            }
        }
    }
    
    // Insert product if no errors
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("INSERT INTO products (name, description, price, category_id, image, stock_quantity, size, color) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$name, $description, $price, $categoryId, $imageName, $stockQuantity, $size, $color]);
            
            setMessage('Product added successfully!', 'success');
            redirect('products.php');
        } catch (PDOException $e) {
            $errors[] = 'Error adding product. Please try again.';
            
            // Delete uploaded image if database insert failed
            if ($imageName && file_exists($uploadDir . $imageName)) {
                unlink($uploadDir . $imageName);
            }
        }
    }
    
    // Display errors
    if (!empty($errors)) {
        foreach ($errors as $error) {
            setMessage($error, 'danger');
        }
    }
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-plus"></i> Add New Product</h1>
        <a href="products.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
    </div>

    <div class="row">
        <div class="col-md-8" style="max-width: 800px; margin: 0 auto;">
            <div class="card">
                <div class="card-header">
                    <h3>Product Information</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" data-validate>
                        <div class="form-group">
                            <label for="name">Product Name *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-control" rows="4"><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div class="form-group">
                                <label for="price">Price *</label>
                                <input type="number" id="price" name="price" class="form-control" 
                                       step="0.01" min="0" value="<?php echo $price ?? ''; ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="category_id">Category *</label>
                                <select id="category_id" name="category_id" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo (isset($categoryId) && $categoryId == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="image">Product Image</label>
                            <input type="file" id="image" name="image" class="form-control" accept="image/*">
                            <small class="form-text text-muted">Upload JPEG, PNG, GIF, or WebP images. Maximum size: 5MB.</small>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                            <div class="form-group">
                                <label for="stock_quantity">Stock Quantity *</label>
                                <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" 
                                       min="0" value="<?php echo $stockQuantity ?? '0'; ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="size">Size</label>
                                <input type="text" id="size" name="size" class="form-control" 
                                       placeholder="e.g., S, M, L, XL" value="<?php echo htmlspecialchars($size ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="color">Color</label>
                                <input type="text" id="color" name="color" class="form-control" 
                                       placeholder="e.g., Red, Blue, Black" value="<?php echo htmlspecialchars($color ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Add Product
                            </button>
                            <a href="products.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Remove existing preview
            const existingPreview = document.getElementById('image-preview');
            if (existingPreview) {
                existingPreview.remove();
            }
            
            // Create new preview
            const preview = document.createElement('div');
            preview.id = 'image-preview';
            preview.style.cssText = 'margin-top: 1rem; text-align: center;';
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <p style="margin-top: 0.5rem; color: #666;">Image Preview</p>
            `;
            
            document.getElementById('image').parentNode.appendChild(preview);
        };
        reader.readAsDataURL(file);
    }
});
</script>

<?php include '../includes/footer.php'; ?>
