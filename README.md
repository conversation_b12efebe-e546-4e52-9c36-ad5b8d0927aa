# Fashion Store - Complete E-commerce Website

A fully functional e-commerce clothing store built with HTML, CSS, JavaScript, and PHP with MySQL database.

## Features

### Customer Features
- **Homepage** with featured products and navigation
- **User Registration & Login** with password hashing and session management
- **Product Catalog** with search, filtering, and sorting
- **Product Details** with image zoom and related products
- **Shopping Cart** with add/remove items, quantity updates, and session persistence
- **User Profile** management with password change
- **Order History** with detailed order tracking
- **Checkout System** with shipping details, payment simulation, and order confirmation
- **Contact Us** page with contact form

### Admin Features
- **Admin Dashboard** with statistics and quick actions
- **Product Management** with CRUD operations and image upload
- **Order Management** with status updates and order details
- **User Management** with role assignments and user statistics

### Technical Features
- **Responsive Design** that works on desktop and mobile
- **Secure Authentication** with password hashing and session management
- **Role-based Access Control** (Customer/Admin)
- **Image Upload** with validation and file management
- **AJAX Cart Operations** for seamless user experience
- **Form Validation** on both client and server side
- **SQL Injection Protection** using prepared statements
- **Clean URL Structure** and organized codebase

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Setup Instructions

1. **Clone/Download the project** to your web server directory

2. **Create Database**
   ```sql
   CREATE DATABASE clothing_store;
   ```

3. **Import Database Schema**
   - Run the SQL commands in `database/schema.sql`
   - This will create all necessary tables and insert sample data

4. **Configure Database Connection**
   - Edit `config/database.php`
   - Update database credentials:
     ```php
     define('DB_HOST', 'localhost');
     define('DB_NAME', 'clothing_store');
     define('DB_USER', 'your_username');
     define('DB_PASS', 'your_password');
     ```

5. **Set Permissions**
   - Ensure `uploads/products/` directory is writable
   - Set appropriate permissions for file uploads

6. **Access the Website**
   - Open your browser and navigate to the project URL
   - Default admin credentials:
     - Email: `<EMAIL>`
     - Password: `admin123`

## Project Structure

```
project/
├── admin/                  # Admin panel pages
│   ├── dashboard.php      # Admin dashboard
│   ├── products.php       # Product management
│   ├── add-product.php    # Add new product
│   ├── orders.php         # Order management
│   └── users.php          # User management
├── ajax/                  # AJAX handlers
│   ├── add_to_cart.php    # Add items to cart
│   ├── update_cart.php    # Update cart quantities
│   └── remove_from_cart.php # Remove items from cart
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database connection
├── css/                   # Stylesheets
│   └── style.css          # Main stylesheet
├── database/              # Database files
│   └── schema.sql         # Database schema and sample data
├── includes/              # Shared components
│   ├── header.php         # Common header
│   └── footer.php         # Common footer
├── js/                    # JavaScript files
│   └── main.js            # Main JavaScript functionality
├── uploads/               # File uploads
│   └── products/          # Product images
├── index.php              # Homepage
├── products.php           # Product listing
├── product.php            # Product details
├── cart.php               # Shopping cart
├── checkout.php           # Checkout process
├── order-confirmation.php # Order confirmation
├── register.php           # User registration
├── login.php              # User login
├── logout.php             # User logout
├── profile.php            # User profile
├── orders.php             # Order history
├── contact.php            # Contact page
└── README.md              # This file
```

## Database Schema

### Tables
- **users** - User accounts with roles
- **categories** - Product categories
- **products** - Product catalog
- **orders** - Customer orders
- **order_items** - Individual order items
- **cart** - Shopping cart items

### Sample Data
The database includes sample data:
- Product categories (Men's, Women's, Accessories, Shoes)
- Sample products with images
- Admin user account

## Security Features

- **Password Hashing** using PHP's `password_hash()`
- **SQL Injection Protection** with prepared statements
- **XSS Prevention** with input sanitization
- **CSRF Protection** through session validation
- **File Upload Security** with type and size validation
- **Role-based Access Control** for admin functions

## Customization

### Adding New Features
1. Create new PHP files following the existing structure
2. Include the header/footer for consistent layout
3. Use the database connection from `config/database.php`
4. Follow the established coding patterns

### Styling
- Modify `css/style.css` for design changes
- The CSS uses a responsive grid system
- Color scheme and components are well-organized

### Database Changes
- Update `database/schema.sql` for new tables
- Use prepared statements for all database operations
- Follow the existing naming conventions

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For support or questions:
- Email: <EMAIL>
- Phone: +****************

---

**Note**: This is a demonstration project. For production use, additional security measures, error handling, and performance optimizations should be implemented.
