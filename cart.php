<?php
$pageTitle = 'Shopping Cart';
include 'includes/header.php';

$db = new Database();
$conn = $db->getConnection();

$cartItems = [];
$total = 0;

if (isLoggedIn()) {
    // Get cart items from database
    $stmt = $conn->prepare("SELECT c.*, p.name, p.price, p.image, p.stock_quantity 
                           FROM cart c 
                           JOIN products p ON c.product_id = p.id 
                           WHERE c.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    // Get cart items from session
    if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
        $productIds = array_keys($_SESSION['cart']);
        $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
        
        $stmt = $conn->prepare("SELECT * FROM products WHERE id IN ($placeholders)");
        $stmt->execute($productIds);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($products as $product) {
            $sessionItem = $_SESSION['cart'][$product['id']];
            $cartItems[] = [
                'product_id' => $product['id'],
                'quantity' => $sessionItem['quantity'],
                'name' => $product['name'],
                'price' => $product['price'],
                'image' => $product['image'],
                'stock_quantity' => $product['stock_quantity']
            ];
        }
    }
}

// Calculate total
foreach ($cartItems as $item) {
    $total += $item['price'] * $item['quantity'];
}
?>

<div class="container" style="margin-top: 2rem;">
    <h1><i class="fas fa-shopping-cart"></i> Shopping Cart</h1>
    
    <?php if (!empty($cartItems)): ?>
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; margin-top: 2rem;">
            <!-- Cart Items -->
            <div class="cart-items">
                <div class="card">
                    <div class="card-header">
                        <h3>Cart Items (<?php echo count($cartItems); ?>)</h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <?php foreach ($cartItems as $item): ?>
                            <div class="cart-item" style="display: flex; align-items: center; padding: 1rem; border-bottom: 1px solid #eee;">
                                <img src="<?php echo file_exists('uploads/products/' . $item['image']) ? 'uploads/products/' . $item['image'] : 'https://via.placeholder.com/100x80?text=No+Image'; ?>" 
                                     alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                     style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-right: 1rem;">
                                
                                <div class="item-details" style="flex: 1;">
                                    <h4 style="margin-bottom: 0.5rem;">
                                        <a href="product.php?id=<?php echo $item['product_id']; ?>" style="text-decoration: none; color: #333;">
                                            <?php echo htmlspecialchars($item['name']); ?>
                                        </a>
                                    </h4>
                                    <p style="color: #666; margin-bottom: 0.5rem;">
                                        Price: <?php echo formatPrice($item['price']); ?>
                                    </p>
                                    <p style="color: #666; font-size: 14px;">
                                        Stock: <?php echo $item['stock_quantity']; ?> available
                                    </p>
                                </div>
                                
                                <div class="quantity-controls" style="display: flex; align-items: center; gap: 1rem; margin-right: 1rem;">
                                    <label for="qty_<?php echo $item['product_id']; ?>" style="font-weight: bold;">Qty:</label>
                                    <input type="number" 
                                           id="qty_<?php echo $item['product_id']; ?>"
                                           class="cart-quantity form-control" 
                                           data-product-id="<?php echo $item['product_id']; ?>"
                                           value="<?php echo $item['quantity']; ?>" 
                                           min="1" 
                                           max="<?php echo $item['stock_quantity']; ?>"
                                           style="width: 80px;">
                                </div>
                                
                                <div class="item-total" style="font-weight: bold; margin-right: 1rem; min-width: 80px; text-align: right;">
                                    <?php echo formatPrice($item['price'] * $item['quantity']); ?>
                                </div>
                                
                                <button class="btn btn-danger remove-from-cart" 
                                        data-product-id="<?php echo $item['product_id']; ?>"
                                        title="Remove from cart">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="cart-summary">
                <div class="card">
                    <div class="card-header">
                        <h3>Order Summary</h3>
                    </div>
                    <div class="card-body">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                            <span>Subtotal:</span>
                            <span><?php echo formatPrice($total); ?></span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                            <span>Shipping:</span>
                            <span><?php echo $total >= 50 ? 'FREE' : formatPrice(9.99); ?></span>
                        </div>
                        
                        <?php if ($total < 50): ?>
                            <div style="font-size: 14px; color: #666; margin-bottom: 1rem;">
                                <i class="fas fa-info-circle"></i> 
                                Add <?php echo formatPrice(50 - $total); ?> more for free shipping!
                            </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div style="display: flex; justify-content: space-between; font-size: 1.2rem; font-weight: bold; margin-bottom: 2rem;">
                            <span>Total:</span>
                            <span><?php echo formatPrice($total + ($total >= 50 ? 0 : 9.99)); ?></span>
                        </div>
                        
                        <?php if (isLoggedIn()): ?>
                            <a href="checkout.php" class="btn btn-primary" style="width: 100%; font-size: 1.1rem; padding: 15px;">
                                <i class="fas fa-credit-card"></i> Proceed to Checkout
                            </a>
                        <?php else: ?>
                            <div class="alert alert-info" style="margin-bottom: 1rem;">
                                Please <a href="login.php">login</a> to proceed with checkout.
                            </div>
                            <a href="login.php" class="btn btn-primary" style="width: 100%; font-size: 1.1rem; padding: 15px;">
                                <i class="fas fa-sign-in-alt"></i> Login to Checkout
                            </a>
                        <?php endif; ?>
                        
                        <a href="products.php" class="btn btn-secondary" style="width: 100%; margin-top: 1rem;">
                            <i class="fas fa-arrow-left"></i> Continue Shopping
                        </a>
                    </div>
                </div>
                
                <!-- Security Badges -->
                <div class="security-badges text-center" style="margin-top: 2rem;">
                    <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
                        <div style="background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <i class="fas fa-lock" style="color: #27ae60;"></i>
                            <div style="font-size: 12px;">Secure Checkout</div>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <i class="fas fa-shield-alt" style="color: #3498db;"></i>
                            <div style="font-size: 12px;">SSL Protected</div>
                        </div>
                        <div style="background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <i class="fas fa-undo" style="color: #f39c12;"></i>
                            <div style="font-size: 12px;">30-Day Returns</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Empty Cart -->
        <div class="empty-cart text-center" style="padding: 4rem 2rem;">
            <i class="fas fa-shopping-cart" style="font-size: 5rem; color: #ccc; margin-bottom: 2rem;"></i>
            <h2>Your cart is empty</h2>
            <p style="color: #666; margin-bottom: 2rem;">Looks like you haven't added any items to your cart yet.</p>
            <a href="products.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 15px 30px;">
                <i class="fas fa-shopping-bag"></i> Start Shopping
            </a>
        </div>
    <?php endif; ?>
</div>

<script>
// Store redirect URL for after login
<?php if (!isLoggedIn()): ?>
    sessionStorage.setItem('redirect_after_login', 'cart.php');
<?php endif; ?>
</script>

<?php include 'includes/footer.php'; ?>
