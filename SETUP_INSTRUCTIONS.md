# Fashion Store - Setup Instructions

## Quick Start Guide

### 1. Database Setup

1. **Create Database:**
   ```sql
   CREATE DATABASE clothing_store;
   ```

2. **Import Schema:**
   - Open `database/schema.sql` in your MySQL client
   - Execute all the SQL commands
   - This creates tables and inserts sample data

### 2. Configuration

1. **Update Database Settings:**
   - Edit `config/database.php`
   - Change these lines to match your setup:
   ```php
   define('DB_HOST', 'localhost');     // Your MySQL host
   define('DB_NAME', 'clothing_store'); // Database name
   define('DB_USER', 'root');          // Your MySQL username
   define('DB_PASS', '');              // Your MySQL password
   ```

2. **File Permissions:**
   - Ensure `uploads/products/` directory exists and is writable
   - Set permissions: `chmod 755 uploads/products/`

### 3. Testing the System

#### Default Admin Account
- **Email:** `<EMAIL>`
- **Password:** `admin123`

#### Test Customer Registration
1. Go to the homepage
2. Click "Register"
3. Create a new customer account
4. Test login with your new account

#### Test Shopping Flow
1. Browse products on the homepage or products page
2. Click on a product to view details
3. Add items to cart
4. View cart and update quantities
5. Proceed to checkout (requires login)
6. Complete the order process

#### Test Admin Features
1. Login with admin credentials
2. Access Admin Panel from the user menu
3. Test product management (add/edit/delete)
4. Test order management (view/update status)
5. Test user management (view users/change roles)

### 4. Sample Data Included

The system comes with:
- **4 Product Categories:** Men's Clothing, Women's Clothing, Accessories, Shoes
- **6 Sample Products** with placeholder images
- **1 Admin User** (credentials above)

### 5. Key Features to Test

#### Customer Features:
- [x] User registration and login
- [x] Product browsing with search and filters
- [x] Shopping cart functionality
- [x] Checkout process with payment simulation
- [x] Order history and tracking
- [x] User profile management
- [x] Contact form

#### Admin Features:
- [x] Dashboard with statistics
- [x] Product CRUD operations
- [x] Image upload for products
- [x] Order management and status updates
- [x] User management and role assignment

#### Technical Features:
- [x] Responsive design (test on mobile)
- [x] AJAX cart operations
- [x] Form validation
- [x] Session management
- [x] Security features (password hashing, SQL injection protection)

### 6. Troubleshooting

#### Common Issues:

1. **Database Connection Error:**
   - Check database credentials in `config/database.php`
   - Ensure MySQL server is running
   - Verify database exists

2. **Image Upload Issues:**
   - Check `uploads/products/` directory permissions
   - Ensure directory exists
   - Check PHP upload settings in php.ini

3. **Session Issues:**
   - Ensure PHP sessions are enabled
   - Check session directory permissions

4. **CSS/JS Not Loading:**
   - Check file paths in includes/header.php
   - Ensure web server can serve static files

### 7. File Structure Overview

```
project/
├── admin/              # Admin panel
├── ajax/               # AJAX handlers
├── config/             # Configuration
├── css/                # Stylesheets
├── database/           # Database schema
├── includes/           # Shared components
├── js/                 # JavaScript
├── uploads/products/   # Product images
├── index.php           # Homepage
├── products.php        # Product listing
├── cart.php            # Shopping cart
├── checkout.php        # Checkout
└── [other pages]       # Various functionality
```

### 8. Next Steps for Production

For a production environment, consider:

1. **Security Enhancements:**
   - Enable HTTPS
   - Implement CSRF tokens
   - Add rate limiting
   - Use environment variables for sensitive data

2. **Performance Optimization:**
   - Enable caching
   - Optimize database queries
   - Compress images
   - Use CDN for static assets

3. **Additional Features:**
   - Email notifications
   - Payment gateway integration
   - Inventory management
   - Advanced reporting
   - Product reviews and ratings

### 9. Support

If you encounter any issues:
1. Check the browser console for JavaScript errors
2. Check PHP error logs
3. Verify database connections
4. Ensure all files are uploaded correctly

The system is designed to be user-friendly and fully functional out of the box with the provided sample data.
