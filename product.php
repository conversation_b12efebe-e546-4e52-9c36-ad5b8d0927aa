<?php
$pageTitle = 'Product Details';
include 'includes/header.php';

// Get product ID
$productId = $_GET['id'] ?? 0;

if (!$productId) {
    setMessage('Product not found.', 'danger');
    redirect('products.php');
}

$db = new Database();
$conn = $db->getConnection();

// Get product details
$stmt = $conn->prepare("SELECT p.*, c.name as category_name 
                       FROM products p 
                       LEFT JOIN categories c ON p.category_id = c.id 
                       WHERE p.id = ?");
$stmt->execute([$productId]);
$product = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$product) {
    setMessage('Product not found.', 'danger');
    redirect('products.php');
}

// Get related products (same category)
$stmt = $conn->prepare("SELECT * FROM products 
                       WHERE category_id = ? AND id != ? 
                       ORDER BY RAND() LIMIT 4");
$stmt->execute([$product['category_id'], $productId]);
$relatedProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

$pageTitle = $product['name'];
?>

<div class="container" style="margin-top: 2rem;">
    <!-- Breadcrumb -->
    <nav style="margin-bottom: 2rem;">
        <ol style="list-style: none; display: flex; gap: 0.5rem; padding: 0; margin: 0;">
            <li><a href="index.php">Home</a></li>
            <li>/</li>
            <li><a href="products.php">Products</a></li>
            <li>/</li>
            <li><a href="products.php?category=<?php echo $product['category_id']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a></li>
            <li>/</li>
            <li style="color: #666;"><?php echo htmlspecialchars($product['name']); ?></li>
        </ol>
    </nav>

    <!-- Product Details -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-bottom: 3rem;">
        <!-- Product Image -->
        <div class="product-image-section">
            <img src="<?php echo file_exists('uploads/products/' . $product['image']) ? 'uploads/products/' . $product['image'] : 'https://via.placeholder.com/500x400?text=No+Image'; ?>" 
                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                 class="product-image" 
                 style="width: 100%; height: 400px; object-fit: cover; border-radius: 8px; cursor: pointer;">
        </div>

        <!-- Product Info -->
        <div class="product-info-section">
            <h1 style="margin-bottom: 1rem;"><?php echo htmlspecialchars($product['name']); ?></h1>
            
            <div class="product-meta" style="margin-bottom: 1rem;">
                <span class="category" style="background: #3498db; color: white; padding: 5px 10px; border-radius: 15px; font-size: 14px;">
                    <?php echo htmlspecialchars($product['category_name']); ?>
                </span>
                
                <?php if ($product['stock_quantity'] <= 0): ?>
                    <span class="stock-status" style="background: #e74c3c; color: white; padding: 5px 10px; border-radius: 15px; font-size: 14px; margin-left: 10px;">
                        Out of Stock
                    </span>
                <?php elseif ($product['stock_quantity'] <= 5): ?>
                    <span class="stock-status" style="background: #f39c12; color: white; padding: 5px 10px; border-radius: 15px; font-size: 14px; margin-left: 10px;">
                        Only <?php echo $product['stock_quantity']; ?> left!
                    </span>
                <?php else: ?>
                    <span class="stock-status" style="background: #27ae60; color: white; padding: 5px 10px; border-radius: 15px; font-size: 14px; margin-left: 10px;">
                        In Stock
                    </span>
                <?php endif; ?>
            </div>

            <div class="product-price" style="font-size: 2rem; color: #e74c3c; font-weight: bold; margin-bottom: 1rem;">
                <?php echo formatPrice($product['price']); ?>
            </div>

            <?php if (!empty($product['description'])): ?>
                <div class="product-description" style="margin-bottom: 2rem; line-height: 1.6;">
                    <h3>Description</h3>
                    <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                </div>
            <?php endif; ?>

            <!-- Product Specifications -->
            <div class="product-specs" style="margin-bottom: 2rem;">
                <h3>Specifications</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <?php if (!empty($product['size'])): ?>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 10px 0; font-weight: bold; width: 30%;">Size:</td>
                            <td style="padding: 10px 0;"><?php echo htmlspecialchars($product['size']); ?></td>
                        </tr>
                    <?php endif; ?>
                    <?php if (!empty($product['color'])): ?>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 10px 0; font-weight: bold;">Color:</td>
                            <td style="padding: 10px 0;"><?php echo htmlspecialchars($product['color']); ?></td>
                        </tr>
                    <?php endif; ?>
                    <tr style="border-bottom: 1px solid #eee;">
                        <td style="padding: 10px 0; font-weight: bold;">Stock:</td>
                        <td style="padding: 10px 0;"><?php echo $product['stock_quantity']; ?> units</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; font-weight: bold;">SKU:</td>
                        <td style="padding: 10px 0;">PRD-<?php echo str_pad($product['id'], 6, '0', STR_PAD_LEFT); ?></td>
                    </tr>
                </table>
            </div>

            <!-- Add to Cart Section -->
            <div class="add-to-cart-section">
                <?php if ($product['stock_quantity'] > 0): ?>
                    <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 1rem;">
                        <label for="quantity" style="font-weight: bold;">Quantity:</label>
                        <input type="number" id="quantity" min="1" max="<?php echo $product['stock_quantity']; ?>" 
                               value="1" style="width: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    
                    <div style="display: flex; gap: 1rem;">
                        <button class="btn btn-success add-to-cart" data-product-id="<?php echo $product['id']; ?>" 
                                style="flex: 1; font-size: 1.1rem; padding: 15px;">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                        <button class="btn btn-primary" onclick="buyNow()" style="flex: 1; font-size: 1.1rem; padding: 15px;">
                            <i class="fas fa-bolt"></i> Buy Now
                        </button>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> This product is currently out of stock.
                    </div>
                    <button class="btn btn-secondary" disabled style="width: 100%; font-size: 1.1rem; padding: 15px;">
                        <i class="fas fa-ban"></i> Out of Stock
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    <?php if (!empty($relatedProducts)): ?>
        <section class="related-products">
            <h2 style="margin-bottom: 2rem;">Related Products</h2>
            <div class="product-grid">
                <?php foreach ($relatedProducts as $relatedProduct): ?>
                    <div class="product-card">
                        <img src="<?php echo file_exists('uploads/products/' . $relatedProduct['image']) ? 'uploads/products/' . $relatedProduct['image'] : 'https://via.placeholder.com/300x250?text=No+Image'; ?>" 
                             alt="<?php echo htmlspecialchars($relatedProduct['name']); ?>" 
                             class="product-image">
                        <div class="product-info">
                            <h3 class="product-name"><?php echo htmlspecialchars($relatedProduct['name']); ?></h3>
                            <p class="product-price"><?php echo formatPrice($relatedProduct['price']); ?></p>
                            <div class="product-actions" style="display: flex; gap: 0.5rem;">
                                <a href="product.php?id=<?php echo $relatedProduct['id']; ?>" class="btn btn-primary" style="flex: 1;">View Details</a>
                                <?php if ($relatedProduct['stock_quantity'] > 0): ?>
                                    <button class="btn btn-success add-to-cart" data-product-id="<?php echo $relatedProduct['id']; ?>">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
    <?php endif; ?>
</div>

<script>
// Update add to cart functionality for this page
document.addEventListener('DOMContentLoaded', function() {
    const addToCartBtn = document.querySelector('.add-to-cart[data-product-id="<?php echo $product['id']; ?>"]');
    const quantityInput = document.getElementById('quantity');
    
    if (addToCartBtn && quantityInput) {
        addToCartBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const quantity = quantityInput.value;
            addToCart(<?php echo $product['id']; ?>, quantity);
        });
    }
});

function buyNow() {
    const quantity = document.getElementById('quantity').value;
    // Add to cart first, then redirect to cart
    fetch('ajax/add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=<?php echo $product['id']; ?>&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = 'cart.php';
        } else {
            showMessage(data.message || 'Error adding product to cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error adding product to cart', 'error');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
