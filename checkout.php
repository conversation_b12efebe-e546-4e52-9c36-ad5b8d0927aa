<?php
$pageTitle = 'Checkout';
include 'includes/header.php';

// Require login
requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get cart items
$cartItems = [];
$total = 0;

$stmt = $conn->prepare("SELECT c.*, p.name, p.price, p.image, p.stock_quantity 
                       FROM cart c 
                       JOIN products p ON c.product_id = p.id 
                       WHERE c.user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($cartItems)) {
    setMessage('Your cart is empty.', 'warning');
    redirect('cart.php');
}

// Calculate totals
foreach ($cartItems as $item) {
    $total += $item['price'] * $item['quantity'];
}

$shipping = $total >= 50 ? 0 : 9.99;
$tax = $total * 0.08; // 8% tax
$grandTotal = $total + $shipping + $tax;

// Get user data for pre-filling
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Handle checkout form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $shippingName = sanitize($_POST['shipping_name'] ?? '');
    $shippingAddress = sanitize($_POST['shipping_address'] ?? '');
    $shippingCity = sanitize($_POST['shipping_city'] ?? '');
    $shippingState = sanitize($_POST['shipping_state'] ?? '');
    $shippingZip = sanitize($_POST['shipping_zip'] ?? '');
    $shippingPhone = sanitize($_POST['shipping_phone'] ?? '');
    
    $paymentMethod = sanitize($_POST['payment_method'] ?? '');
    $cardNumber = sanitize($_POST['card_number'] ?? '');
    $cardExpiry = sanitize($_POST['card_expiry'] ?? '');
    $cardCvv = sanitize($_POST['card_cvv'] ?? '');
    $cardName = sanitize($_POST['card_name'] ?? '');
    
    $errors = [];
    
    // Validation
    if (empty($shippingName) || empty($shippingAddress) || empty($shippingCity) || 
        empty($shippingState) || empty($shippingZip) || empty($shippingPhone)) {
        $errors[] = 'All shipping fields are required.';
    }
    
    if (empty($paymentMethod)) {
        $errors[] = 'Please select a payment method.';
    }
    
    if ($paymentMethod === 'credit_card') {
        if (empty($cardNumber) || empty($cardExpiry) || empty($cardCvv) || empty($cardName)) {
            $errors[] = 'All credit card fields are required.';
        }
        // Basic card number validation (should be 16 digits)
        if (!empty($cardNumber) && !preg_match('/^\d{16}$/', str_replace(' ', '', $cardNumber))) {
            $errors[] = 'Please enter a valid 16-digit card number.';
        }
    }
    
    // Check stock availability one more time
    foreach ($cartItems as $item) {
        $stmt = $conn->prepare("SELECT stock_quantity FROM products WHERE id = ?");
        $stmt->execute([$item['product_id']]);
        $currentStock = $stmt->fetchColumn();
        
        if ($currentStock < $item['quantity']) {
            $errors[] = "Insufficient stock for {$item['name']}. Only {$currentStock} available.";
        }
    }
    
    // Process order if no errors
    if (empty($errors)) {
        try {
            $conn->beginTransaction();
            
            // Create order
            $fullShippingAddress = "$shippingName\n$shippingAddress\n$shippingCity, $shippingState $shippingZip\nPhone: $shippingPhone";
            
            $stmt = $conn->prepare("INSERT INTO orders (user_id, total_amount, shipping_address, payment_method, payment_status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$_SESSION['user_id'], $grandTotal, $fullShippingAddress, $paymentMethod, 'completed']);
            $orderId = $conn->lastInsertId();
            
            // Create order items and update stock
            foreach ($cartItems as $item) {
                // Insert order item
                $stmt = $conn->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
                $stmt->execute([$orderId, $item['product_id'], $item['quantity'], $item['price']]);
                
                // Update product stock
                $stmt = $conn->prepare("UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?");
                $stmt->execute([$item['quantity'], $item['product_id']]);
            }
            
            // Clear user's cart
            $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            
            $conn->commit();
            
            // Simulate payment processing delay
            sleep(2);
            
            setMessage('Order placed successfully! Thank you for your purchase.', 'success');
            redirect("order-confirmation.php?order_id=$orderId");
            
        } catch (Exception $e) {
            $conn->rollBack();
            $errors[] = 'Error processing order. Please try again.';
        }
    }
    
    // Display errors
    if (!empty($errors)) {
        foreach ($errors as $error) {
            setMessage($error, 'danger');
        }
    }
}
?>

<div class="container" style="margin-top: 2rem;">
    <h1><i class="fas fa-credit-card"></i> Checkout</h1>
    
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; margin-top: 2rem;">
        <!-- Checkout Form -->
        <div class="checkout-form">
            <form method="POST" data-validate id="checkout-form">
                <!-- Shipping Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h3><i class="fas fa-shipping-fast"></i> Shipping Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="shipping_name">Full Name *</label>
                            <input type="text" id="shipping_name" name="shipping_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="shipping_address">Street Address *</label>
                            <input type="text" id="shipping_address" name="shipping_address" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>" required>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                            <div class="form-group">
                                <label for="shipping_city">City *</label>
                                <input type="text" id="shipping_city" name="shipping_city" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="shipping_state">State *</label>
                                <input type="text" id="shipping_state" name="shipping_state" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="shipping_zip">ZIP Code *</label>
                                <input type="text" id="shipping_zip" name="shipping_zip" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="shipping_phone">Phone Number *</label>
                            <input type="tel" id="shipping_phone" name="shipping_phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" required>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h3><i class="fas fa-credit-card"></i> Payment Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>Payment Method *</label>
                            <div style="display: flex; gap: 1rem; margin-top: 0.5rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="radio" name="payment_method" value="credit_card" required>
                                    <i class="fas fa-credit-card"></i> Credit Card
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="radio" name="payment_method" value="paypal">
                                    <i class="fab fa-paypal"></i> PayPal
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="radio" name="payment_method" value="cash_on_delivery">
                                    <i class="fas fa-money-bill"></i> Cash on Delivery
                                </label>
                            </div>
                        </div>
                        
                        <div id="credit-card-fields" style="display: none;">
                            <div class="form-group">
                                <label for="card_name">Cardholder Name *</label>
                                <input type="text" id="card_name" name="card_name" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="card_number">Card Number *</label>
                                <input type="text" id="card_number" name="card_number" class="form-control" 
                                       placeholder="1234 5678 9012 3456" maxlength="19">
                            </div>
                            
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label for="card_expiry">Expiry Date *</label>
                                    <input type="text" id="card_expiry" name="card_expiry" class="form-control" 
                                           placeholder="MM/YY" maxlength="5">
                                </div>
                                
                                <div class="form-group">
                                    <label for="card_cvv">CVV *</label>
                                    <input type="text" id="card_cvv" name="card_cvv" class="form-control" 
                                           placeholder="123" maxlength="4">
                                </div>
                            </div>
                        </div>
                        
                        <div id="paypal-info" style="display: none; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                            <p><i class="fab fa-paypal"></i> You will be redirected to PayPal to complete your payment.</p>
                        </div>
                        
                        <div id="cod-info" style="display: none; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                            <p><i class="fas fa-info-circle"></i> You will pay when your order is delivered.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Place Order Button -->
                <button type="submit" class="btn btn-success" style="width: 100%; font-size: 1.2rem; padding: 15px;" id="place-order-btn">
                    <i class="fas fa-lock"></i> Place Order - <?php echo formatPrice($grandTotal); ?>
                </button>
            </form>
        </div>
        
        <!-- Order Summary -->
        <div class="order-summary">
            <div class="card">
                <div class="card-header">
                    <h3>Order Summary</h3>
                </div>
                <div class="card-body">
                    <!-- Cart Items -->
                    <div class="order-items" style="margin-bottom: 1rem;">
                        <?php foreach ($cartItems as $item): ?>
                            <div style="display: flex; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f0f0f0;">
                                <img src="<?php echo file_exists('uploads/products/' . $item['image']) ? 'uploads/products/' . $item['image'] : 'https://via.placeholder.com/50x50?text=No+Image'; ?>" 
                                     alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px; margin-right: 1rem;">
                                
                                <div style="flex: 1;">
                                    <h5 style="margin-bottom: 0.25rem; font-size: 14px;"><?php echo htmlspecialchars($item['name']); ?></h5>
                                    <p style="color: #666; margin: 0; font-size: 12px;">Qty: <?php echo $item['quantity']; ?></p>
                                </div>
                                
                                <div style="font-weight: bold; font-size: 14px;">
                                    <?php echo formatPrice($item['price'] * $item['quantity']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Order Totals -->
                    <div class="order-totals">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Subtotal:</span>
                            <span><?php echo formatPrice($total); ?></span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Shipping:</span>
                            <span><?php echo $shipping > 0 ? formatPrice($shipping) : 'FREE'; ?></span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Tax:</span>
                            <span><?php echo formatPrice($tax); ?></span>
                        </div>
                        
                        <hr>
                        
                        <div style="display: flex; justify-content: space-between; font-size: 1.2rem; font-weight: bold;">
                            <span>Total:</span>
                            <span><?php echo formatPrice($grandTotal); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Security Info -->
            <div class="security-info text-center" style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                <i class="fas fa-shield-alt" style="color: #27ae60; font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <p style="margin: 0; font-size: 14px;">Your payment information is secure and encrypted.</p>
            </div>
        </div>
    </div>
</div>

<script>
// Payment method handling
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const creditCardFields = document.getElementById('credit-card-fields');
    const paypalInfo = document.getElementById('paypal-info');
    const codInfo = document.getElementById('cod-info');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Hide all payment info sections
            creditCardFields.style.display = 'none';
            paypalInfo.style.display = 'none';
            codInfo.style.display = 'none';
            
            // Show relevant section
            if (this.value === 'credit_card') {
                creditCardFields.style.display = 'block';
            } else if (this.value === 'paypal') {
                paypalInfo.style.display = 'block';
            } else if (this.value === 'cash_on_delivery') {
                codInfo.style.display = 'block';
            }
        });
    });
    
    // Card number formatting
    const cardNumberInput = document.getElementById('card_number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            let value = this.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            this.value = formattedValue;
        });
    }
    
    // Expiry date formatting
    const expiryInput = document.getElementById('card_expiry');
    if (expiryInput) {
        expiryInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            this.value = value;
        });
    }
    
    // Form submission handling
    const form = document.getElementById('checkout-form');
    const submitBtn = document.getElementById('place-order-btn');
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        submitBtn.disabled = true;
    });
});
</script>

<?php include 'includes/footer.php'; ?>
