<?php
$pageTitle = 'Manage Orders';
include '../includes/header.php';

// Require admin access
requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $orderId = intval($_POST['order_id']);
    $newStatus = sanitize($_POST['status']);
    
    $allowedStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
    
    if (in_array($newStatus, $allowedStatuses)) {
        try {
            $stmt = $conn->prepare("UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$newStatus, $orderId]);
            setMessage('Order status updated successfully.', 'success');
        } catch (PDOException $e) {
            setMessage('Error updating order status.', 'danger');
        }
    }
    
    redirect('orders.php');
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';
$sort = $_GET['sort'] ?? 'created_at';
$order = $_GET['order'] ?? 'DESC';

// Build query
$whereConditions = [];
$params = [];

if (!empty($status)) {
    $whereConditions[] = "o.status = ?";
    $params[] = $status;
}

if (!empty($search)) {
    $whereConditions[] = "(u.name LIKE ? OR u.email LIKE ? OR o.id LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Validate sort and order
$allowedSorts = ['id', 'total_amount', 'status', 'created_at'];
$allowedOrders = ['ASC', 'DESC'];
$sort = in_array($sort, $allowedSorts) ? $sort : 'created_at';
$order = in_array($order, $allowedOrders) ? $order : 'DESC';

$query = "SELECT o.*, u.name as customer_name, u.email as customer_email 
          FROM orders o 
          JOIN users u ON o.user_id = u.id 
          $whereClause 
          ORDER BY o.$sort $order";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get status counts
$stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM orders GROUP BY status");
$stmt->execute();
$statusCounts = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $statusCounts[$row['status']] = $row['count'];
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-shopping-cart"></i> Manage Orders</h1>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <!-- Status Overview -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
        <div class="stat-card" style="background: #f39c12; color: white; padding: 1rem; border-radius: 8px; text-align: center;">
            <h3><?php echo $statusCounts['pending'] ?? 0; ?></h3>
            <p>Pending</p>
        </div>
        <div class="stat-card" style="background: #3498db; color: white; padding: 1rem; border-radius: 8px; text-align: center;">
            <h3><?php echo $statusCounts['processing'] ?? 0; ?></h3>
            <p>Processing</p>
        </div>
        <div class="stat-card" style="background: #9b59b6; color: white; padding: 1rem; border-radius: 8px; text-align: center;">
            <h3><?php echo $statusCounts['shipped'] ?? 0; ?></h3>
            <p>Shipped</p>
        </div>
        <div class="stat-card" style="background: #27ae60; color: white; padding: 1rem; border-radius: 8px; text-align: center;">
            <h3><?php echo $statusCounts['delivered'] ?? 0; ?></h3>
            <p>Delivered</p>
        </div>
        <div class="stat-card" style="background: #e74c3c; color: white; padding: 1rem; border-radius: 8px; text-align: center;">
            <h3><?php echo $statusCounts['cancelled'] ?? 0; ?></h3>
            <p>Cancelled</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="d-flex gap-2" style="flex-wrap: wrap; align-items: end;">
                <div class="form-group" style="min-width: 200px;">
                    <label for="search">Search Orders</label>
                    <input type="text" id="search" name="search" class="form-control" 
                           placeholder="Search by customer name, email, or order ID..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="form-group" style="min-width: 150px;">
                    <label for="status">Status</label>
                    <select id="status" name="status" class="form-control">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                        <option value="shipped" <?php echo $status === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                        <option value="delivered" <?php echo $status === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                        <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 120px;">
                    <label for="sort">Sort By</label>
                    <select id="sort" name="sort" class="form-control">
                        <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Date</option>
                        <option value="id" <?php echo $sort === 'id' ? 'selected' : ''; ?>>Order ID</option>
                        <option value="total_amount" <?php echo $sort === 'total_amount' ? 'selected' : ''; ?>>Amount</option>
                        <option value="status" <?php echo $sort === 'status' ? 'selected' : ''; ?>>Status</option>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 100px;">
                    <label for="order">Order</label>
                    <select id="order" name="order" class="form-control">
                        <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>Descending</option>
                        <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="orders.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <?php if (!empty($orders)): ?>
        <div class="card">
            <div class="card-header">
                <h3>Orders (<?php echo count($orders); ?> found)</h3>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Payment</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td><strong>#<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?></strong></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                        <br><small style="color: #666;"><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($order['created_at'])); ?></td>
                                    <td><strong><?php echo formatPrice($order['total_amount']); ?></strong></td>
                                    <td>
                                        <span class="badge btn-<?php echo $order['payment_status'] === 'completed' ? 'success' : ($order['payment_status'] === 'failed' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($order['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <select name="status" class="form-control form-control-sm" style="width: auto; display: inline-block;" onchange="this.form.submit()">
                                                <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                                <option value="shipped" <?php echo $order['status'] === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                                <option value="delivered" <?php echo $order['status'] === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                                <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                    </td>
                                    <td>
                                        <button class="btn btn-info btn-sm" onclick="viewOrderDetails(<?php echo $order['id']; ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-shopping-cart" style="font-size: 4rem; color: #ccc; margin-bottom: 1rem;"></i>
            <h3>No Orders Found</h3>
            <p>Try adjusting your search criteria.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Order Details Modal -->
<div id="orderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; max-width: 800px; width: 90%; max-height: 90%; overflow-y: auto;">
        <div style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
            <h3 id="modalTitle">Order Details</h3>
            <button onclick="closeModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
        </div>
        <div id="modalContent" style="padding: 1rem;">
            <!-- Order details will be loaded here -->
        </div>
    </div>
</div>

<script>
function viewOrderDetails(orderId) {
    // In a real application, this would fetch order details via AJAX
    document.getElementById('modalTitle').textContent = 'Order #' + orderId.toString().padStart(6, '0');
    document.getElementById('modalContent').innerHTML = '<p>Loading order details...</p>';
    document.getElementById('orderModal').style.display = 'block';
    
    // Simulate loading
    setTimeout(() => {
        document.getElementById('modalContent').innerHTML = `
            <p><strong>Order ID:</strong> #${orderId.toString().padStart(6, '0')}</p>
            <p><strong>Status:</strong> Processing</p>
            <p><strong>Total:</strong> $99.99</p>
            <p>Order details would be loaded here via AJAX in a real application.</p>
        `;
    }, 500);
}

function closeModal() {
    document.getElementById('orderModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('orderModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
