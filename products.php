<?php
$pageTitle = 'Products';
include 'includes/header.php';

$db = new Database();
$conn = $db->getConnection();

// Get filter parameters
$category = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';
$sort = $_GET['sort'] ?? 'name';
$order = $_GET['order'] ?? 'ASC';

// Build query
$whereConditions = [];
$params = [];

if (!empty($category)) {
    $whereConditions[] = "p.category_id = ?";
    $params[] = $category;
}

if (!empty($search)) {
    $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Validate sort and order
$allowedSorts = ['name', 'price', 'created_at'];
$allowedOrders = ['ASC', 'DESC'];
$sort = in_array($sort, $allowedSorts) ? $sort : 'name';
$order = in_array($order, $allowedOrders) ? $order : 'ASC';

$query = "SELECT p.*, c.name as category_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          $whereClause 
          ORDER BY p.$sort $order";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get categories for filter
$stmt = $conn->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container" style="margin-top: 2rem;">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-tshirt"></i> Products</h1>
        <div class="product-count">
            <span class="badge" style="background: #3498db; color: white; padding: 8px 12px; border-radius: 20px;">
                <?php echo count($products); ?> Products Found
            </span>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="d-flex gap-2" style="flex-wrap: wrap; align-items: end;">
                <div class="form-group" style="min-width: 200px;">
                    <label for="search">Search Products</label>
                    <input type="text" id="search" name="search" class="form-control" 
                           placeholder="Search by name or description..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="form-group" style="min-width: 150px;">
                    <label for="category">Category</label>
                    <select id="category" name="category" class="form-control">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" 
                                    <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 120px;">
                    <label for="sort">Sort By</label>
                    <select id="sort" name="sort" class="form-control">
                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                        <option value="price" <?php echo $sort === 'price' ? 'selected' : ''; ?>>Price</option>
                        <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Newest</option>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 100px;">
                    <label for="order">Order</label>
                    <select id="order" name="order" class="form-control">
                        <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                        <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>Descending</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Grid -->
    <?php if (!empty($products)): ?>
        <div class="product-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <div style="position: relative;">
                        <img src="<?php echo file_exists('uploads/products/' . $product['image']) ? 'uploads/products/' . $product['image'] : 'https://via.placeholder.com/300x250?text=No+Image'; ?>" 
                             alt="<?php echo htmlspecialchars($product['name']); ?>" 
                             class="product-image">
                        
                        <?php if ($product['stock_quantity'] <= 0): ?>
                            <div style="position: absolute; top: 10px; right: 10px; background: #e74c3c; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                                Out of Stock
                            </div>
                        <?php elseif ($product['stock_quantity'] <= 5): ?>
                            <div style="position: absolute; top: 10px; right: 10px; background: #f39c12; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                                Low Stock
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-info">
                        <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                        <p class="product-category" style="color: #666; margin-bottom: 0.5rem;">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </p>
                        
                        <?php if (!empty($product['description'])): ?>
                            <p class="product-description" style="color: #777; font-size: 14px; margin-bottom: 0.5rem;">
                                <?php echo htmlspecialchars(substr($product['description'], 0, 100)) . (strlen($product['description']) > 100 ? '...' : ''); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="product-details" style="display: flex; gap: 1rem; margin-bottom: 0.5rem; font-size: 14px; color: #666;">
                            <?php if (!empty($product['size'])): ?>
                                <span><strong>Size:</strong> <?php echo htmlspecialchars($product['size']); ?></span>
                            <?php endif; ?>
                            <?php if (!empty($product['color'])): ?>
                                <span><strong>Color:</strong> <?php echo htmlspecialchars($product['color']); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <p class="product-price"><?php echo formatPrice($product['price']); ?></p>
                        
                        <div class="product-actions" style="display: flex; gap: 0.5rem;">
                            <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-primary" style="flex: 1;">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            <?php if ($product['stock_quantity'] > 0): ?>
                                <button class="btn btn-success add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                    <i class="fas fa-cart-plus"></i>
                                </button>
                            <?php else: ?>
                                <button class="btn btn-secondary" disabled>
                                    <i class="fas fa-ban"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-search" style="font-size: 4rem; color: #ccc; margin-bottom: 1rem;"></i>
            <h3>No Products Found</h3>
            <p>Try adjusting your search criteria or browse all products.</p>
            <a href="products.php" class="btn btn-primary">View All Products</a>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
