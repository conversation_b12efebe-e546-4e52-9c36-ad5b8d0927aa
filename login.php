<?php
$pageTitle = 'Login';
include 'includes/header.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    $errors = [];
    
    // Validation
    if (empty($email)) {
        $errors[] = 'Email is required.';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    }
    
    // Authenticate user
    if (empty($errors)) {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("SELECT id, name, email, password, role FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // Login successful
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['name'] = $user['name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            
            // Handle remember me
            if ($remember) {
                $token = bin2hex(random_bytes(32));
                // In a real application, you would store this token in the database
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
            }
            
            // Merge session cart with user cart if exists
            if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
                foreach ($_SESSION['cart'] as $productId => $item) {
                    // Check if item already exists in user's cart
                    $stmt = $conn->prepare("SELECT quantity FROM cart WHERE user_id = ? AND product_id = ?");
                    $stmt->execute([$user['id'], $productId]);
                    $existingItem = $stmt->fetch();
                    
                    if ($existingItem) {
                        // Update quantity
                        $newQuantity = $existingItem['quantity'] + $item['quantity'];
                        $stmt = $conn->prepare("UPDATE cart SET quantity = ? WHERE user_id = ? AND product_id = ?");
                        $stmt->execute([$newQuantity, $user['id'], $productId]);
                    } else {
                        // Insert new item
                        $stmt = $conn->prepare("INSERT INTO cart (user_id, product_id, quantity) VALUES (?, ?, ?)");
                        $stmt->execute([$user['id'], $productId, $item['quantity']]);
                    }
                }
                unset($_SESSION['cart']);
            }
            
            setMessage('Welcome back, ' . $user['name'] . '!', 'success');
            
            // Redirect based on role
            if ($user['role'] === 'admin') {
                redirect('admin/dashboard.php');
            } else {
                // Redirect to intended page or home
                $redirectTo = $_SESSION['redirect_after_login'] ?? 'index.php';
                unset($_SESSION['redirect_after_login']);
                redirect($redirectTo);
            }
        } else {
            $errors[] = 'Invalid email or password.';
        }
    }
    
    // Display errors
    if (!empty($errors)) {
        foreach ($errors as $error) {
            setMessage($error, 'danger');
        }
    }
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="row">
        <div class="col-md-6" style="max-width: 400px; margin: 0 auto;">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-sign-in-alt"></i> Login</h2>
                </div>
                <div class="card-body">
                    <form method="POST" data-validate>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label" style="display: flex; align-items: center; gap: 0.5rem;">
                                <input type="checkbox" name="remember" value="1">
                                Remember me
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Don't have an account? <a href="register.php">Register here</a></p>
                        <p><a href="forgot-password.php">Forgot your password?</a></p>
                    </div>
                    
                    <!-- Demo Credentials -->
                    <div class="alert alert-info mt-3">
                        <strong>Demo Credentials:</strong><br>
                        <strong>Admin:</strong> <EMAIL> / admin123<br>
                        <strong>Customer:</strong> Create a new account
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
