<?php
$pageTitle = 'Contact Us';
include 'includes/header.php';

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        setMessage('Please fill in all fields.', 'danger');
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        setMessage('Please enter a valid email address.', 'danger');
    } else {
        // In a real application, you would send an email or save to database
        setMessage('Thank you for your message! We will get back to you soon.', 'success');
        
        // Clear form data
        $name = $email = $subject = $message = '';
    }
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="row">
        <div class="col-md-8" style="max-width: 800px; margin: 0 auto;">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-envelope"></i> Contact Us</h2>
                </div>
                <div class="card-body">
                    <p>We'd love to hear from you! Send us a message and we'll respond as soon as possible.</p>
                    
                    <form method="POST" data-validate>
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">Subject *</label>
                            <input type="text" id="subject" name="subject" class="form-control" 
                                   value="<?php echo htmlspecialchars($subject ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" class="form-control" rows="6" required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contact Information -->
    <div class="row" style="margin-top: 3rem;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
            <div class="contact-info text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-map-marker-alt" style="font-size: 2rem; color: #3498db; margin-bottom: 1rem;"></i>
                <h3>Address</h3>
                <p>123 Fashion Street<br>Style City, SC 12345<br>United States</p>
            </div>
            
            <div class="contact-info text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-phone" style="font-size: 2rem; color: #27ae60; margin-bottom: 1rem;"></i>
                <h3>Phone</h3>
                <p>+****************<br>Mon - Fri: 9AM - 6PM<br>Sat - Sun: 10AM - 4PM</p>
            </div>
            
            <div class="contact-info text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-envelope" style="font-size: 2rem; color: #e74c3c; margin-bottom: 1rem;"></i>
                <h3>Email</h3>
                <p><EMAIL><br><EMAIL><br><EMAIL></p>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
