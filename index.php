<?php
$pageTitle = 'Home';
include 'includes/header.php';

// Get featured products
$db = new Database();
$conn = $db->getConnection();
$stmt = $conn->prepare("SELECT p.*, c.name as category_name FROM products p 
                       LEFT JOIN categories c ON p.category_id = c.id 
                       ORDER BY p.created_at DESC LIMIT 6");
$stmt->execute();
$featuredProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!-- Hero Section -->
<section class="hero" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; text-align: center; margin-bottom: 3rem;">
    <div class="container">
        <h1 style="font-size: 3rem; margin-bottom: 1rem;">Welcome to <?php echo SITE_NAME; ?></h1>
        <p style="font-size: 1.2rem; margin-bottom: 2rem;">Discover the latest fashion trends and timeless classics</p>
        <a href="products.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 15px 30px;">Shop Now</a>
    </div>
</section>

<!-- Features Section -->
<section class="features" style="margin-bottom: 3rem;">
    <div class="container">
        <h2 class="text-center mb-3">Why Choose Us?</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
            <div class="feature-card text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-shipping-fast" style="font-size: 3rem; color: #3498db; margin-bottom: 1rem;"></i>
                <h3>Fast Shipping</h3>
                <p>Free shipping on orders over $50. Get your items delivered quickly and safely.</p>
            </div>
            <div class="feature-card text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-undo" style="font-size: 3rem; color: #27ae60; margin-bottom: 1rem;"></i>
                <h3>Easy Returns</h3>
                <p>30-day return policy. Not satisfied? Return your items hassle-free.</p>
            </div>
            <div class="feature-card text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-star" style="font-size: 3rem; color: #f39c12; margin-bottom: 1rem;"></i>
                <h3>Quality Products</h3>
                <p>Carefully curated collection of high-quality clothing and accessories.</p>
            </div>
            <div class="feature-card text-center" style="padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <i class="fas fa-headset" style="font-size: 3rem; color: #e74c3c; margin-bottom: 1rem;"></i>
                <h3>24/7 Support</h3>
                <p>Our customer service team is here to help you anytime, anywhere.</p>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-products">
    <div class="container">
        <h2 class="text-center mb-3">Featured Products</h2>
        <?php if (!empty($featuredProducts)): ?>
            <div class="product-grid">
                <?php foreach ($featuredProducts as $product): ?>
                    <div class="product-card">
                        <img src="<?php echo file_exists('uploads/products/' . $product['image']) ? 'uploads/products/' . $product['image'] : 'https://via.placeholder.com/300x250?text=No+Image'; ?>" 
                             alt="<?php echo htmlspecialchars($product['name']); ?>" 
                             class="product-image">
                        <div class="product-info">
                            <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                            <p class="product-category" style="color: #666; margin-bottom: 0.5rem;">
                                <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            <p class="product-price"><?php echo formatPrice($product['price']); ?></p>
                            <div class="product-actions" style="display: flex; gap: 0.5rem;">
                                <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-primary" style="flex: 1;">View Details</a>
                                <?php if ($product['stock_quantity'] > 0): ?>
                                    <button class="btn btn-success add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-secondary" disabled>Out of Stock</button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="text-center mt-3">
                <a href="products.php" class="btn btn-primary">View All Products</a>
            </div>
        <?php else: ?>
            <div class="text-center">
                <p>No products available at the moment.</p>
                <a href="products.php" class="btn btn-primary">Browse Products</a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter" style="background: #34495e; color: white; padding: 3rem 0; margin-top: 3rem;">
    <div class="container text-center">
        <h2>Stay Updated</h2>
        <p>Subscribe to our newsletter for the latest fashion trends and exclusive offers.</p>
        <form style="max-width: 400px; margin: 0 auto; display: flex; gap: 1rem;">
            <input type="email" placeholder="Enter your email" class="form-control" style="flex: 1;">
            <button type="submit" class="btn btn-primary">Subscribe</button>
        </form>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
