<?php
$pageTitle = 'Order Confirmation';
include 'includes/header.php';

// Require login
requireLogin();

$orderId = $_GET['order_id'] ?? 0;

if (!$orderId) {
    setMessage('Order not found.', 'danger');
    redirect('orders.php');
}

$db = new Database();
$conn = $db->getConnection();

// Get order details
$stmt = $conn->prepare("SELECT * FROM orders WHERE id = ? AND user_id = ?");
$stmt->execute([$orderId, $_SESSION['user_id']]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    setMessage('Order not found.', 'danger');
    redirect('orders.php');
}

// Get order items
$stmt = $conn->prepare("SELECT oi.*, p.name, p.image 
                       FROM order_items oi 
                       JOIN products p ON oi.product_id = p.id 
                       WHERE oi.order_id = ?");
$stmt->execute([$orderId]);
$orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container" style="margin-top: 2rem;">
    <!-- Success Message -->
    <div class="text-center" style="margin-bottom: 3rem;">
        <i class="fas fa-check-circle" style="font-size: 5rem; color: #27ae60; margin-bottom: 1rem;"></i>
        <h1 style="color: #27ae60; margin-bottom: 1rem;">Order Confirmed!</h1>
        <p style="font-size: 1.2rem; color: #666;">Thank you for your purchase. Your order has been successfully placed.</p>
    </div>

    <!-- Order Details -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
        <!-- Order Information -->
        <div class="order-details">
            <div class="card">
                <div class="card-header">
                    <h3>Order Details</h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                        <div>
                            <h4>Order Information</h4>
                            <p><strong>Order Number:</strong> #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?></p>
                            <p><strong>Order Date:</strong> <?php echo date('F j, Y \a\t g:i A', strtotime($order['created_at'])); ?></p>
                            <p><strong>Status:</strong> 
                                <span class="badge btn-<?php echo $order['status'] === 'pending' ? 'warning' : 'success'; ?>">
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                            </p>
                            <p><strong>Payment Status:</strong> 
                                <span class="badge btn-success"><?php echo ucfirst($order['payment_status']); ?></span>
                            </p>
                        </div>
                        
                        <div>
                            <h4>Shipping Information</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                                <?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Items -->
                    <h4>Order Items</h4>
                    <div class="order-items">
                        <?php foreach ($orderItems as $item): ?>
                            <div style="display: flex; align-items: center; padding: 1rem 0; border-bottom: 1px solid #eee;">
                                <img src="<?php echo file_exists('uploads/products/' . $item['image']) ? 'uploads/products/' . $item['image'] : 'https://via.placeholder.com/80x80?text=No+Image'; ?>" 
                                     alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                     style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-right: 1rem;">
                                
                                <div style="flex: 1;">
                                    <h5 style="margin-bottom: 0.5rem;"><?php echo htmlspecialchars($item['name']); ?></h5>
                                    <p style="color: #666; margin: 0;">
                                        Quantity: <?php echo $item['quantity']; ?> × <?php echo formatPrice($item['price']); ?>
                                    </p>
                                </div>
                                
                                <div style="font-weight: bold; font-size: 1.1rem;">
                                    <?php echo formatPrice($item['price'] * $item['quantity']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Summary -->
        <div class="order-summary">
            <div class="card">
                <div class="card-header">
                    <h3>Order Summary</h3>
                </div>
                <div class="card-body">
                    <?php
                    $subtotal = 0;
                    foreach ($orderItems as $item) {
                        $subtotal += $item['price'] * $item['quantity'];
                    }
                    $shipping = $subtotal >= 50 ? 0 : 9.99;
                    $tax = $subtotal * 0.08;
                    ?>
                    
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Subtotal:</span>
                        <span><?php echo formatPrice($subtotal); ?></span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Shipping:</span>
                        <span><?php echo $shipping > 0 ? formatPrice($shipping) : 'FREE'; ?></span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Tax:</span>
                        <span><?php echo formatPrice($tax); ?></span>
                    </div>
                    
                    <hr>
                    
                    <div style="display: flex; justify-content: space-between; font-size: 1.2rem; font-weight: bold;">
                        <span>Total:</span>
                        <span><?php echo formatPrice($order['total_amount']); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="card" style="margin-top: 1rem;">
                <div class="card-header">
                    <h3>What's Next?</h3>
                </div>
                <div class="card-body">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <i class="fas fa-envelope" style="color: #3498db; font-size: 1.5rem;"></i>
                            <div>
                                <strong>Confirmation Email</strong>
                                <p style="margin: 0; color: #666; font-size: 14px;">We've sent a confirmation email to your registered email address.</p>
                            </div>
                        </div>
                        
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <i class="fas fa-box" style="color: #f39c12; font-size: 1.5rem;"></i>
                            <div>
                                <strong>Order Processing</strong>
                                <p style="margin: 0; color: #666; font-size: 14px;">Your order will be processed within 1-2 business days.</p>
                            </div>
                        </div>
                        
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <i class="fas fa-truck" style="color: #27ae60; font-size: 1.5rem;"></i>
                            <div>
                                <strong>Shipping</strong>
                                <p style="margin: 0; color: #666; font-size: 14px;">You'll receive tracking information once your order ships.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="text-center" style="margin: 3rem 0;">
        <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
            <a href="orders.php" class="btn btn-primary">
                <i class="fas fa-list"></i> View All Orders
            </a>
            <a href="products.php" class="btn btn-success">
                <i class="fas fa-shopping-cart"></i> Continue Shopping
            </a>
            <button class="btn btn-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> Print Order
            </button>
            <button class="btn btn-info" onclick="downloadInvoice()">
                <i class="fas fa-download"></i> Download Invoice
            </button>
        </div>
    </div>
    
    <!-- Customer Support -->
    <div class="card" style="margin-bottom: 2rem;">
        <div class="card-body text-center">
            <h4>Need Help?</h4>
            <p>If you have any questions about your order, please don't hesitate to contact us.</p>
            <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
                <div>
                    <i class="fas fa-phone" style="color: #3498db;"></i>
                    <strong> Call:</strong> +****************
                </div>
                <div>
                    <i class="fas fa-envelope" style="color: #3498db;"></i>
                    <strong> Email:</strong> <EMAIL>
                </div>
                <div>
                    <i class="fas fa-comments" style="color: #3498db;"></i>
                    <strong> Live Chat:</strong> Available 24/7
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadInvoice() {
    // In a real application, this would generate and download a PDF invoice
    alert('Invoice download for Order #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?>');
}

// Auto-scroll to top on page load
window.addEventListener('load', function() {
    window.scrollTo(0, 0);
});
</script>

<?php include 'includes/footer.php'; ?>
