<?php
$pageTitle = 'Manage Products';
include '../includes/header.php';

// Require admin access
requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Handle product deletion
if (isset($_GET['delete']) && $_GET['delete']) {
    $productId = intval($_GET['delete']);
    
    try {
        // Get product image to delete file
        $stmt = $conn->prepare("SELECT image FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Delete product
        $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        
        // Delete image file if exists
        if ($product && $product['image'] && file_exists('../uploads/products/' . $product['image'])) {
            unlink('../uploads/products/' . $product['image']);
        }
        
        setMessage('Product deleted successfully.', 'success');
    } catch (PDOException $e) {
        setMessage('Error deleting product. It may be referenced in orders.', 'danger');
    }
    
    redirect('products.php');
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$sort = $_GET['sort'] ?? 'name';
$order = $_GET['order'] ?? 'ASC';

// Build query
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category)) {
    $whereConditions[] = "p.category_id = ?";
    $params[] = $category;
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Validate sort and order
$allowedSorts = ['name', 'price', 'stock_quantity', 'created_at'];
$allowedOrders = ['ASC', 'DESC'];
$sort = in_array($sort, $allowedSorts) ? $sort : 'name';
$order = in_array($order, $allowedOrders) ? $order : 'ASC';

$query = "SELECT p.*, c.name as category_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          $whereClause 
          ORDER BY p.$sort $order";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get categories for filter
$stmt = $conn->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container" style="margin-top: 2rem;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-box"></i> Manage Products</h1>
        <div>
            <a href="add-product.php" class="btn btn-success">
                <i class="fas fa-plus"></i> Add New Product
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="d-flex gap-2" style="flex-wrap: wrap; align-items: end;">
                <div class="form-group" style="min-width: 200px;">
                    <label for="search">Search Products</label>
                    <input type="text" id="search" name="search" class="form-control" 
                           placeholder="Search by name or description..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="form-group" style="min-width: 150px;">
                    <label for="category">Category</label>
                    <select id="category" name="category" class="form-control">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" 
                                    <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 120px;">
                    <label for="sort">Sort By</label>
                    <select id="sort" name="sort" class="form-control">
                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                        <option value="price" <?php echo $sort === 'price' ? 'selected' : ''; ?>>Price</option>
                        <option value="stock_quantity" <?php echo $sort === 'stock_quantity' ? 'selected' : ''; ?>>Stock</option>
                        <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Date Added</option>
                    </select>
                </div>
                
                <div class="form-group" style="min-width: 100px;">
                    <label for="order">Order</label>
                    <select id="order" name="order" class="form-control">
                        <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                        <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>Descending</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <?php if (!empty($products)): ?>
        <div class="card">
            <div class="card-header">
                <h3>Products (<?php echo count($products); ?> found)</h3>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>
                                        <img src="<?php echo file_exists('../uploads/products/' . $product['image']) ? '../uploads/products/' . $product['image'] : 'https://via.placeholder.com/60x60?text=No+Image'; ?>" 
                                             alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                        <?php if (!empty($product['description'])): ?>
                                            <br><small style="color: #666;"><?php echo htmlspecialchars(substr($product['description'], 0, 50)) . (strlen($product['description']) > 50 ? '...' : ''); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'No Category'); ?></td>
                                    <td><?php echo formatPrice($product['price']); ?></td>
                                    <td>
                                        <span class="badge btn-<?php echo $product['stock_quantity'] <= 0 ? 'danger' : ($product['stock_quantity'] <= 5 ? 'warning' : 'success'); ?>">
                                            <?php echo $product['stock_quantity']; ?> units
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($product['stock_quantity'] > 0): ?>
                                            <span class="badge btn-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge btn-danger">Out of Stock</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div style="display: flex; gap: 0.5rem;">
                                            <a href="../product.php?id=<?php echo $product['id']; ?>" 
                                               class="btn btn-info btn-sm" title="View Product" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-product.php?id=<?php echo $product['id']; ?>"
                                               class="btn btn-primary btn-sm" title="Edit Product">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="products.php?delete=<?php echo $product['id']; ?>" 
                                               class="btn btn-danger btn-sm" title="Delete Product"
                                               onclick="return confirm('Are you sure you want to delete this product?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="text-center" style="padding: 3rem;">
            <i class="fas fa-box-open" style="font-size: 4rem; color: #ccc; margin-bottom: 1rem;"></i>
            <h3>No Products Found</h3>
            <p>Try adjusting your search criteria or add a new product.</p>
            <a href="add-product.php" class="btn btn-primary">Add New Product</a>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
