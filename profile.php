<?php
$pageTitle = 'My Profile';
include 'includes/header.php';

// Require login
requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    setMessage('User not found.', 'danger');
    redirect('logout.php');
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $address = sanitize($_POST['address'] ?? '');
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    // Validation
    if (empty($name)) {
        $errors[] = 'Name is required.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    // Check if email is already taken by another user
    if (empty($errors) && $email !== $user['email']) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $_SESSION['user_id']]);
        if ($stmt->fetch()) {
            $errors[] = 'Email address is already taken.';
        }
    }
    
    // Password validation (only if changing password)
    if (!empty($newPassword)) {
        if (empty($currentPassword)) {
            $errors[] = 'Current password is required to change password.';
        } elseif (!password_verify($currentPassword, $user['password'])) {
            $errors[] = 'Current password is incorrect.';
        } elseif (strlen($newPassword) < 6) {
            $errors[] = 'New password must be at least 6 characters long.';
        } elseif ($newPassword !== $confirmPassword) {
            $errors[] = 'New passwords do not match.';
        }
    }
    
    // Update profile if no errors
    if (empty($errors)) {
        try {
            if (!empty($newPassword)) {
                // Update with new password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, phone = ?, address = ?, password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$name, $email, $phone, $address, $hashedPassword, $_SESSION['user_id']]);
            } else {
                // Update without password change
                $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$name, $email, $phone, $address, $_SESSION['user_id']]);
            }
            
            // Update session data
            $_SESSION['name'] = $name;
            $_SESSION['email'] = $email;
            
            setMessage('Profile updated successfully!', 'success');
            
            // Refresh user data
            $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $errors[] = 'Error updating profile. Please try again.';
        }
    }
    
    // Display errors
    if (!empty($errors)) {
        foreach ($errors as $error) {
            setMessage($error, 'danger');
        }
    }
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="row">
        <div class="col-md-8" style="max-width: 800px; margin: 0 auto;">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-user"></i> My Profile</h2>
                </div>
                <div class="card-body">
                    <!-- Profile Info -->
                    <div class="profile-info" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div class="avatar" style="width: 80px; height: 80px; background: #3498db; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
                                <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                            </div>
                            <div>
                                <h3 style="margin-bottom: 0.5rem;"><?php echo htmlspecialchars($user['name']); ?></h3>
                                <p style="color: #666; margin-bottom: 0.5rem;"><?php echo htmlspecialchars($user['email']); ?></p>
                                <p style="color: #666; font-size: 14px;">
                                    Member since <?php echo date('F Y', strtotime($user['created_at'])); ?>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <span class="badge" style="background: #e74c3c; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">Admin</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Update Profile Form -->
                    <form method="POST" data-validate>
                        <h3 style="margin-bottom: 1rem;">Personal Information</h3>
                        
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="address">Address</label>
                            <textarea id="address" name="address" class="form-control" rows="3"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                        </div>
                        
                        <hr style="margin: 2rem 0;">
                        
                        <h3 style="margin-bottom: 1rem;">Change Password</h3>
                        <p style="color: #666; margin-bottom: 1rem;">Leave password fields empty if you don't want to change your password.</p>
                        
                        <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <input type="password" id="current_password" name="current_password" class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">New Password</label>
                            <input type="password" id="new_password" name="new_password" class="form-control" minlength="6">
                            <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control">
                        </div>
                        
                        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Home
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Account Actions -->
            <div class="card" style="margin-top: 2rem;">
                <div class="card-header">
                    <h3>Account Actions</h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <a href="orders.php" class="btn btn-primary">
                            <i class="fas fa-shopping-bag"></i> View Orders
                        </a>
                        <a href="cart.php" class="btn btn-success">
                            <i class="fas fa-shopping-cart"></i> View Cart
                        </a>
                        <?php if (isAdmin()): ?>
                            <a href="admin/dashboard.php" class="btn btn-warning">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        <?php endif; ?>
                        <a href="logout.php" class="btn btn-danger" onclick="return confirm('Are you sure you want to logout?')">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
