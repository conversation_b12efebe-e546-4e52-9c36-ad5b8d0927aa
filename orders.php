<?php
$pageTitle = 'My Orders';
include 'includes/header.php';

// Require login
requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get user's orders
$stmt = $conn->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Function to get status badge class
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'processing': return 'info';
        case 'shipped': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

// Function to get status icon
function getStatusIcon($status) {
    switch ($status) {
        case 'pending': return 'fas fa-clock';
        case 'processing': return 'fas fa-cog fa-spin';
        case 'shipped': return 'fas fa-truck';
        case 'delivered': return 'fas fa-check-circle';
        case 'cancelled': return 'fas fa-times-circle';
        default: return 'fas fa-question-circle';
    }
}
?>

<div class="container" style="margin-top: 2rem;">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1><i class="fas fa-shopping-bag"></i> My Orders</h1>
        <a href="products.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Shop More
        </a>
    </div>

    <?php if (!empty($orders)): ?>
        <div class="orders-list">
            <?php foreach ($orders as $order): ?>
                <?php
                // Get order items
                $stmt = $conn->prepare("SELECT oi.*, p.name, p.image 
                                       FROM order_items oi 
                                       JOIN products p ON oi.product_id = p.id 
                                       WHERE oi.order_id = ?");
                $stmt->execute([$order['id']]);
                $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
                ?>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                            <div>
                                <h4 style="margin-bottom: 0.5rem;">Order #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?></h4>
                                <p style="color: #666; margin: 0;">
                                    Placed on <?php echo date('F j, Y \a\t g:i A', strtotime($order['created_at'])); ?>
                                </p>
                            </div>
                            <div style="text-align: right;">
                                <span class="badge btn-<?php echo getStatusBadgeClass($order['status']); ?>" 
                                      style="padding: 8px 12px; font-size: 14px;">
                                    <i class="<?php echo getStatusIcon($order['status']); ?>"></i>
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                                <div style="font-size: 1.2rem; font-weight: bold; margin-top: 0.5rem;">
                                    <?php echo formatPrice($order['total_amount']); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- Order Items -->
                        <div class="order-items" style="margin-bottom: 1rem;">
                            <?php foreach ($orderItems as $item): ?>
                                <div style="display: flex; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f0f0f0;">
                                    <img src="<?php echo file_exists('uploads/products/' . $item['image']) ? 'uploads/products/' . $item['image'] : 'https://via.placeholder.com/60x60?text=No+Image'; ?>" 
                                         alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                         style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; margin-right: 1rem;">
                                    
                                    <div style="flex: 1;">
                                        <h5 style="margin-bottom: 0.25rem;"><?php echo htmlspecialchars($item['name']); ?></h5>
                                        <p style="color: #666; margin: 0;">
                                            Quantity: <?php echo $item['quantity']; ?> × <?php echo formatPrice($item['price']); ?>
                                        </p>
                                    </div>
                                    
                                    <div style="font-weight: bold;">
                                        <?php echo formatPrice($item['price'] * $item['quantity']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Order Details -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div>
                                <h5>Shipping Address</h5>
                                <p style="color: #666; margin: 0;"><?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
                            </div>
                            
                            <div>
                                <h5>Payment Information</h5>
                                <p style="color: #666; margin-bottom: 0.5rem;">
                                    Method: <?php echo htmlspecialchars($order['payment_method'] ?? 'Not specified'); ?>
                                </p>
                                <p style="color: #666; margin: 0;">
                                    Status: 
                                    <span class="badge btn-<?php echo $order['payment_status'] === 'completed' ? 'success' : ($order['payment_status'] === 'failed' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($order['payment_status']); ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Order Actions -->
                        <div style="margin-top: 1rem; display: flex; gap: 1rem; flex-wrap: wrap;">
                            <button class="btn btn-primary" onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                <i class="fas fa-eye"></i> View Details
                            </button>
                            
                            <?php if ($order['status'] === 'delivered'): ?>
                                <button class="btn btn-success" onclick="reorderItems(<?php echo $order['id']; ?>)">
                                    <i class="fas fa-redo"></i> Reorder
                                </button>
                            <?php endif; ?>
                            
                            <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                                <button class="btn btn-danger" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                    <i class="fas fa-times"></i> Cancel Order
                                </button>
                            <?php endif; ?>
                            
                            <button class="btn btn-secondary" onclick="downloadInvoice(<?php echo $order['id']; ?>)">
                                <i class="fas fa-download"></i> Download Invoice
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Order Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h3>Order Statistics</h3>
            </div>
            <div class="card-body">
                <?php
                $totalOrders = count($orders);
                $totalSpent = array_sum(array_column($orders, 'total_amount'));
                $statusCounts = array_count_values(array_column($orders, 'status'));
                ?>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div class="stat-card text-center" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-shopping-bag" style="font-size: 2rem; color: #3498db; margin-bottom: 0.5rem;"></i>
                        <h4><?php echo $totalOrders; ?></h4>
                        <p>Total Orders</p>
                    </div>
                    
                    <div class="stat-card text-center" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-dollar-sign" style="font-size: 2rem; color: #27ae60; margin-bottom: 0.5rem;"></i>
                        <h4><?php echo formatPrice($totalSpent); ?></h4>
                        <p>Total Spent</p>
                    </div>
                    
                    <div class="stat-card text-center" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #f39c12; margin-bottom: 0.5rem;"></i>
                        <h4><?php echo $statusCounts['delivered'] ?? 0; ?></h4>
                        <p>Delivered Orders</p>
                    </div>
                    
                    <div class="stat-card text-center" style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-clock" style="font-size: 2rem; color: #e74c3c; margin-bottom: 0.5rem;"></i>
                        <h4><?php echo ($statusCounts['pending'] ?? 0) + ($statusCounts['processing'] ?? 0); ?></h4>
                        <p>Pending Orders</p>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- No Orders -->
        <div class="text-center" style="padding: 4rem 2rem;">
            <i class="fas fa-shopping-bag" style="font-size: 5rem; color: #ccc; margin-bottom: 2rem;"></i>
            <h2>No Orders Yet</h2>
            <p style="color: #666; margin-bottom: 2rem;">You haven't placed any orders yet. Start shopping to see your order history here.</p>
            <a href="products.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 15px 30px;">
                <i class="fas fa-shopping-cart"></i> Start Shopping
            </a>
        </div>
    <?php endif; ?>
</div>

<script>
function viewOrderDetails(orderId) {
    // In a real application, this would open a detailed order view
    alert('Order details for Order #' + orderId.toString().padStart(6, '0'));
}

function reorderItems(orderId) {
    if (confirm('Add all items from this order to your cart?')) {
        // In a real application, this would add all order items to cart
        showMessage('Items added to cart!', 'success');
    }
}

function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // In a real application, this would cancel the order
        showMessage('Order cancelled successfully.', 'success');
        setTimeout(() => location.reload(), 1000);
    }
}

function downloadInvoice(orderId) {
    // In a real application, this would generate and download a PDF invoice
    alert('Invoice download for Order #' + orderId.toString().padStart(6, '0'));
}
</script>

<?php include 'includes/footer.php'; ?>
